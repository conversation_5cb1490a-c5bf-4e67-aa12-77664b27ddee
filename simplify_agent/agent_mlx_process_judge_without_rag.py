#!/usr/bin/env python3
"""
测试过程智能评价器 (MLX版本)
用于评价已完成的测试任务是否满足初始要求，并给出改进建议
使用 MLX 框架进行本地推理，避免网络依赖
"""

import json
import re
import time
import os
import sys
import threading
import multiprocessing
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# 设置多进程启动方法为spawn，避免fork相关问题
try:
    multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    # 如果已经设置过，就忽略
    pass

# 导入 MLX 相关库
try:
    import mlx_lm
    MLX_AVAILABLE = True
    print("✅ MLX 框架加载成功")
except ImportError as e:
    print(f"❌ MLX 框架未安装: {e}")
    print("请安装: pip install mlx-lm")
    MLX_AVAILABLE = False

# 添加父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 默认配置常量
DEFAULT_MODEL_PATH = "/Users/<USER>/Desktop/work/ai_models/lm_studio/Qwen/Qwen3-Coder-30B-A3B-Instruct-MLX-4bit"
DEFAULT_MAX_TOKENS = 8000

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestProcessJudge:
    """测试过程智能评价器 (MLX版本)"""
    
    # 类级别的线程锁，确保只有一个请求能同时使用模型
    _model_lock = threading.Lock()
    
    def __init__(self, model_path: str = DEFAULT_MODEL_PATH, 
                 max_tokens: int = DEFAULT_MAX_TOKENS):
        """
        初始化评价器
        
        Args:
            model_path: MLX模型路径
            max_tokens: 最大生成token数
        """
        if not MLX_AVAILABLE:
            raise RuntimeError("MLX框架未安装，请运行: pip install mlx-lm")
            
        self.model_path = model_path
        self.max_tokens = max_tokens
        
        # 加载prompt模板
        self.prompt_templates = self._load_prompt_templates()
        
        # 延迟加载模型（在第一次使用时加载）
        self.model = None
        self.tokenizer = None
        self._model_loaded = False
    
    def _load_prompt_templates(self) -> Dict[str, Any]:
        """加载prompt模板文件"""
        try:
            # 从当前目录加载prompt模板
            current_dir = os.path.dirname(os.path.abspath(__file__))
            prompt_file = os.path.join(current_dir, "judge_prompt.json")
            
            if not os.path.exists(prompt_file):
                raise FileNotFoundError(f"Prompt模板文件不存在: {prompt_file}")
            
            with open(prompt_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
                
            logger.info(f"✅ Prompt模板加载成功: {prompt_file}")
            return templates
            
        except Exception as e:
            logger.error(f"❌ 加载prompt模板失败: {e}")
            raise
    
    def _ensure_model_loaded(self):
        """确保模型已加载"""
        if not self._model_loaded:
            try:
                logger.info(f"🔄 开始加载MLX模型: {self.model_path}")
                
                # 检查模型路径是否存在
                if not os.path.exists(self.model_path):
                    raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
                
                # 加载MLX模型和tokenizer
                self.model, self.tokenizer = mlx_lm.load(self.model_path)
                self._model_loaded = True
                logger.info(f"✅ MLX模型加载成功")
                
            except Exception as e:
                logger.error(f"❌ MLX模型加载失败: {e}")
                raise
    
    def _call_llm(self, prompt: str, system_prompt: str = None) -> Dict[str, str]:
        """调用MLX模型进行分析"""
        with self._model_lock:
            try:
                # 确保模型已加载
                self._ensure_model_loaded()
                
                # 构建完整prompt
                if system_prompt:
                    full_prompt = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
                else:
                    full_prompt = f"<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
                
                logger.info("🔄 开始MLX模型推理...")
                
                # 使用MLX进行生成
                generation_result = self._generate_with_streaming_stop(
                    full_prompt=full_prompt,
                    stop_tokens=['<|endoftext|>', '</s>', '<|end|>', '<|im_end|>'],
                    show_progress=True  # 显示生成进度，让用户看到评价过程
                )
                
                # 处理生成结果
                if isinstance(generation_result, dict):
                    raw_content = generation_result.get('text', '')
                else:
                    raw_content = generation_result
                
                # 清理输出内容
                raw_content = raw_content.strip()

                # 分离思考过程和最终输出
                thinking_content = ""
                final_content = raw_content

                # 提取 <think> 标签内容
                think_pattern = r'<think>(.*?)</think>'
                think_match = re.search(think_pattern, raw_content, re.DOTALL)
                if think_match:
                    thinking_content = think_match.group(1).strip()
                    # 移除原文中的 <think> 标签
                    final_content = re.sub(think_pattern, '', raw_content, flags=re.DOTALL).strip()

                # 尝试解析JSON格式的输出
                parsed_json = self._extract_json_from_response(final_content)

                return {
                    "thinking": thinking_content,
                    "analysis": final_content,
                    "parsed_json": parsed_json,
                    "has_structured_output": parsed_json is not None
                }
                
            except Exception as e:
                logger.error(f"MLX模型调用异常: {e}")
                return {
                    "thinking": "",
                    "analysis": f"❌ MLX模型调用异常: {str(e)}"
                }
    
    def _generate_with_streaming_stop(self, full_prompt: str, stop_tokens: List[str] = None, show_progress: bool = True) -> str:
        """
        使用流式生成，实时检测停止标记并立即停止生成
        
        Args:
            full_prompt: 完整的提示词
            stop_tokens: 停止标记列表
            show_progress: 是否显示生成进度
        
        Returns:
            生成的文本（截止到停止标记之前）
        """
        if stop_tokens is None:
            stop_tokens = ['<|endoftext|>', '</s>', '<|im_end|>']
        
        try:
            generated_text = ""
            detection_buffer = ""
            buffer_size = 50
            token_count = 0
            
            # 开始流式生成
            stream = mlx_lm.stream_generate(
                model=self.model,
                tokenizer=self.tokenizer,
                prompt=full_prompt,
                max_tokens=self.max_tokens
            )
            
            start_time = time.time()
            
            for response in stream:
                # MLX stream_generate返回GenerationResponse对象，需要获取其text属性
                if hasattr(response, 'text') and response.text:
                    token_text = response.text
                elif isinstance(response, str):
                    token_text = response
                else:
                    continue
                
                if token_text:
                    generated_text += token_text
                    detection_buffer += token_text
                    token_count += 1
                    
                    if show_progress:
                        print(token_text, end='', flush=True)
                    
                    # 保持检测缓冲区大小
                    if len(detection_buffer) > buffer_size:
                        detection_buffer = detection_buffer[-buffer_size:]
                    
                    # 检测停止标记
                    stop_detected = False
                    detected_token = None
                    for stop_token in stop_tokens:
                        if stop_token in detection_buffer or stop_token in generated_text[-len(stop_token):]:
                            stop_detected = True
                            detected_token = stop_token
                            
                            # 截断到停止标记之前的内容
                            stop_index = generated_text.rfind(stop_token)
                            if stop_index > 0:
                                generated_text = generated_text[:stop_index]
                            break
                    
                    if stop_detected:
                        elapsed = time.time() - start_time
                        logger.info(f"🏁 MLX生成已停止 - 检测到: {detected_token}")
                        logger.info(f"📊 生成统计: {token_count} tokens, {len(generated_text)} 字符, 耗时: {elapsed:.2f}秒")
                        break
            else:
                # 正常结束
                elapsed = time.time() - start_time
                logger.info(f"✅ MLX生成正常结束")
                logger.info(f"📊 生成统计: {token_count} tokens, {len(generated_text)} 字符, 耗时: {elapsed:.2f}秒")
            
            return generated_text.strip()
            
        except Exception as e:
            logger.error(f"MLX流式生成异常: {e}")
            return f"❌ MLX生成异常: {str(e)}"

    def _extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """从响应中提取JSON格式的评价结果，参照planner的简化逻辑"""
        try:
            logger.info(f"🔍 开始JSON提取，文本长度: {len(response_text)} 字符")

            # 清理思维链标签
            cleaned_response = response_text
            think_pattern = r'<think>.*?</think>'
            cleaned_response = re.sub(think_pattern, '', cleaned_response, flags=re.DOTALL)

            # 预处理：检查是否包含结束标记
            end_tokens = ['<|endoftext|>', '</s>', '<|end|>', '<|im_end|>']
            end_position = len(cleaned_response)
            found_end_token = None

            for token in end_tokens:
                pos = cleaned_response.find(token)
                if pos != -1 and pos < end_position:
                    end_position = pos
                    found_end_token = token

            if found_end_token:
                logger.info(f"✂️ 发现结束标记 '{found_end_token}' 在位置 {end_position}，截断处理")
                cleaned_response = cleaned_response[:end_position]

            # 简化策略：直接查找最大的完整JSON对象
            # 找到第一个{的位置
            first_brace = cleaned_response.find('{')
            if first_brace == -1:
                logger.info("😞 未找到JSON开始标记 {")
                return None

            # 从第一个{开始，使用括号匹配找到完整的JSON
            brace_count = 0
            json_end = -1

            for i in range(first_brace, len(cleaned_response)):
                char = cleaned_response[i]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        json_end = i + 1
                        break

            if json_end == -1:
                logger.info("😞 未找到JSON结束标记，括号不匹配")
                return None

            # 提取完整的JSON字符串
            json_content = cleaned_response[first_brace:json_end]
            logger.info(f"🎯 提取到JSON内容，长度: {len(json_content)} 字符")
            logger.info(f"🔍 JSON开头: {json_content[:100]}...")
            logger.info(f"🔍 JSON结尾: {json_content[-100:]}")

            # 清理和解析JSON
            cleaned_json = self._clean_json_string(json_content)
            data = json.loads(cleaned_json)

            logger.info(f"✅ JSON解析成功，顶级字段: {list(data.keys())}")

            # 验证结构
            if self._validate_evaluation_structure(data):
                logger.info(f"✅ JSON结构验证通过")
                return data
            else:
                logger.info(f"❌ JSON结构验证失败")
                return None

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.error(f"JSON提取异常: {e}")
            return None

    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串中的常见问题"""
        # 移除前后空白
        json_str = json_str.strip()

        # 修复常见的格式问题
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号

        return json_str

    def _validate_evaluation_structure(self, data: Dict[str, Any]) -> bool:
        """验证评价结构是否有效"""
        try:
            # 检查必需的顶级字段
            required_fields = ['evaluation_summary', 'dimension_scores', 'detailed_analysis']

            for field in required_fields:
                if field not in data:
                    logger.info(f"🔍 缺少必需字段: {field}")
                    return False

            # 检查evaluation_summary结构
            summary = data.get('evaluation_summary', {})
            if not isinstance(summary, dict):
                return False

            # 检查dimension_scores结构
            scores = data.get('dimension_scores', {})
            if not isinstance(scores, dict):
                return False

            # 检查detailed_analysis结构
            analysis = data.get('detailed_analysis', {})
            if not isinstance(analysis, dict):
                return False

            logger.info(f"✅ 评价结构验证通过")
            return True

        except Exception as e:
            logger.info(f"🔍 评价结构验证异常: {str(e)}")
            return False

    def _format_structured_evaluation(self, evaluation_data: Dict[str, Any]) -> str:
        """格式化结构化的评价数据"""
        try:
            formatted_sections = []

            # 总体评价结果
            summary = evaluation_data.get('evaluation_summary', {})
            if summary:
                formatted_sections.append(f"""
{'='*80}
总体评价结果
{'='*80}

🎯 最终成功状态: {summary.get('overall_success', '未知')}
📊 成功评分: {summary.get('overall_score', 0.0):.2f}
🔍 置信度: {summary.get('confidence_level', 0.0):.2f}

🔑 关键发现:""")

                key_findings = summary.get('key_findings', [])
                for i, finding in enumerate(key_findings, 1):
                    formatted_sections.append(f"  {i}. {finding}")

            # 分维度评分
            scores = evaluation_data.get('dimension_scores', {})
            if scores:
                formatted_sections.append(f"""

{'='*80}
分维度评分
{'='*80}

📋 测试计划质量评分: {scores.get('plan_quality_score', 0.0):.2f}
🎯 执行符合度评分: {scores.get('execution_compliance_score', 0.0):.2f}
⚡ 执行质量评分: {scores.get('execution_quality_score', 0.0):.2f}
🏆 目标达成度评分: {scores.get('goal_achievement_score', 0.0):.2f}""")

            # 详细分析
            detailed_analysis = evaluation_data.get('detailed_analysis', {})
            if detailed_analysis:
                # 测试计划质量分析
                plan_analysis = detailed_analysis.get('plan_quality_analysis', {})
                if plan_analysis:
                    formatted_sections.append(f"""

{'='*80}
1. 测试计划质量分析
{'='*80}

{plan_analysis.get('analysis_content', '')}

🔍 关键问题:""")
                    for issue in plan_analysis.get('key_issues', []):
                        formatted_sections.append(f"  • {issue}")

                    formatted_sections.append("\n💡 改进建议:")
                    for suggestion in plan_analysis.get('improvement_suggestions', []):
                        formatted_sections.append(f"  • {suggestion}")

                # 执行符合度分析
                compliance_analysis = detailed_analysis.get('execution_compliance_analysis', {})
                if compliance_analysis:
                    formatted_sections.append(f"""

{'='*80}
2. 执行符合度分析
{'='*80}

{compliance_analysis.get('analysis_content', '')}

🔍 关键问题:""")
                    for issue in compliance_analysis.get('key_issues', []):
                        formatted_sections.append(f"  • {issue}")

                    formatted_sections.append("\n💡 改进建议:")
                    for suggestion in compliance_analysis.get('improvement_suggestions', []):
                        formatted_sections.append(f"  • {suggestion}")

                # 执行质量分析
                quality_analysis = detailed_analysis.get('execution_quality_analysis', {})
                if quality_analysis:
                    formatted_sections.append(f"""

{'='*80}
3. 执行质量分析
{'='*80}

{quality_analysis.get('analysis_content', '')}

🔍 关键问题:""")
                    for issue in quality_analysis.get('key_issues', []):
                        formatted_sections.append(f"  • {issue}")

                    formatted_sections.append("\n💡 改进建议:")
                    for suggestion in quality_analysis.get('improvement_suggestions', []):
                        formatted_sections.append(f"  • {suggestion}")

                # 目标达成度分析
                goal_analysis = detailed_analysis.get('goal_achievement_analysis', {})
                if goal_analysis:
                    formatted_sections.append(f"""

{'='*80}
4. 目标达成度分析
{'='*80}

{goal_analysis.get('analysis_content', '')}

🔍 关键问题:""")
                    for issue in goal_analysis.get('key_issues', []):
                        formatted_sections.append(f"  • {issue}")

                    formatted_sections.append("\n💡 改进建议:")
                    for suggestion in goal_analysis.get('improvement_suggestions', []):
                        formatted_sections.append(f"  • {suggestion}")

            # 综合评价与建议
            comprehensive = evaluation_data.get('comprehensive_evaluation', {})
            if comprehensive:
                formatted_sections.append(f"""

{'='*80}
综合评价与建议
{'='*80}

🔍 根本原因:""")
                for cause in comprehensive.get('root_causes', []):
                    formatted_sections.append(f"  • {cause}")

                formatted_sections.append("\n✨ 最佳实践:")
                for practice in comprehensive.get('best_practices', []):
                    formatted_sections.append(f"  • {practice}")

                formatted_sections.append("\n🚀 改进方向:")
                for direction in comprehensive.get('improvement_directions', []):
                    formatted_sections.append(f"  • {direction}")

                formatted_sections.append("\n💡 具体建议:")
                for recommendation in comprehensive.get('recommendations', []):
                    formatted_sections.append(f"  • {recommendation}")

            return "\n".join(formatted_sections)

        except Exception as e:
            logger.error(f"格式化结构化评价失败: {e}")
            return f"\n\n❌ 格式化结构化评价失败: {str(e)}"

    def parse_test_log(self, log_file_path: str) -> Dict[str, Any]:
        """解析测试日志文件"""
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 先从日志内容中提取原始指令
            original_instruction = self._extract_original_instruction(content)
            
            # 如果没找到，或者找到的是格式化后的指令，尝试从 agent.log 中获取
            if original_instruction == "未找到原始指令" or "🎯 测试计划摘要" in original_instruction:
                folder_path = str(Path(log_file_path).parent)
                agent_log_instruction = self._find_original_instruction_from_agent_log(folder_path)
                if agent_log_instruction:
                    original_instruction = agent_log_instruction
                    logger.info(f"✅ 使用从 agent.log 和测试计划获取的原始指令")
            
            # 提取基本信息
            log_data = {
                "log_path": log_file_path,
                "parse_time": datetime.now().isoformat(),
                "original_instruction": original_instruction,
                "round_number": self._extract_round_number_from_folder(str(Path(log_file_path).parent)),
                "test_plan": self._extract_test_plan(content),
                "execution_rounds": self._extract_execution_rounds(content),
                "completion_info": self._extract_completion_info(content),
                "raw_content": content
            }
            
            return log_data
            
        except Exception as e:
            logger.error(f"解析日志文件失败: {e}")
            return {"error": str(e), "log_path": log_file_path}
    
    def _extract_original_instruction(self, content: str) -> str:
        """提取原始用户指令"""
        # 查找原始用户输入
        pattern = r'用户输入:.*?(?=\n=|$)'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            instruction = match.group(0).replace('用户输入:', '').strip()
            # 清理格式化标记
            instruction = re.sub(r'={40,}', '', instruction)
            instruction = re.sub(r'🎯.*?\n', '', instruction)
            instruction = re.sub(r'📝.*?\n', '', instruction)
            instruction = re.sub(r'📱.*?\n', '', instruction)
            instruction = re.sub(r'🔢.*?\n', '', instruction)
            instruction = re.sub(r'📋.*?\n', '', instruction)
            return instruction.strip()
        
        return "未找到原始指令"
    
    def _extract_round_number_from_folder(self, folder_path: str) -> Optional[int]:
        """从文件夹名称提取轮次编号"""
        folder_name = Path(folder_path).name
        # 匹配格式: round_000371_20250801_152136
        pattern = r'round_(\d+)_'
        match = re.search(pattern, folder_name)
        if match:
            return int(match.group(1))
        return None
    
    def _find_original_instruction_from_agent_log(self, round_folder_path: str) -> Optional[str]:
        """从 agent.log 中提取测试计划文件路径，然后从 JSON 计划文件获取原始请求"""
        try:
            agent_log_path = Path(round_folder_path) / "agent.log"
            if not agent_log_path.exists():
                logger.warning(f"agent.log 文件不存在: {agent_log_path}")
                return None
            
            # 从 agent.log 中提取测试计划文件路径
            plan_file_path = self._extract_plan_file_from_agent_log(str(agent_log_path))
            if not plan_file_path:
                logger.warning("从 agent.log 中未找到测试计划文件路径")
                return None
            
            # 从测试计划 JSON 文件中加载原始请求
            original_request = self._load_original_request_from_plan(plan_file_path)
            if original_request:
                logger.info(f"✅ 从测试计划文件获取到原始请求: {plan_file_path}")
                return original_request
            
            return None
            
        except Exception as e:
            logger.error(f"从 agent.log 获取原始指令失败: {e}")
            return None
    
    def _extract_plan_file_from_agent_log(self, agent_log_path: str) -> Optional[str]:
        """从 agent.log 中提取测试计划文件路径"""
        try:
            with open(agent_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找“📂 加载测试计划文件:”行
            pattern = r'📂 加载测试计划文件: (.+)'
            match = re.search(pattern, content)
            if match:
                plan_file_path = match.group(1).strip()
                # 转换为绝对路径
                if not os.path.isabs(plan_file_path):
                    # 如果是相对路径，基于当前工作目录进行转换
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    parent_dir = os.path.dirname(current_dir)
                    plan_file_path = os.path.join(parent_dir, plan_file_path)
                
                logger.info(f"提取到测试计划文件路径: {plan_file_path}")
                return plan_file_path
            
            return None
            
        except Exception as e:
            logger.error(f"从 agent.log 提取测试计划文件路径失败: {e}")
            return None
    
    def _load_original_request_from_plan(self, plan_file_path: str) -> Optional[str]:
        """从测试计划 JSON 文件中加载 original_request"""
        try:
            if not os.path.exists(plan_file_path):
                logger.warning(f"测试计划文件不存在: {plan_file_path}")
                return None
            
            with open(plan_file_path, 'r', encoding='utf-8') as f:
                plan_data = json.load(f)
            
            # 从 JSON 中获取 original_request
            original_request = plan_data.get('original_request')
            if original_request:
                logger.info(f"成功从测试计划文件加载 original_request")
                return original_request
            
            # 如果没有 original_request，尝试从 structured_plan.original_request 获取
            structured_plan = plan_data.get('structured_plan', {})
            original_request = structured_plan.get('original_request')
            if original_request:
                logger.info(f"成功从测试计划的 structured_plan 加载 original_request")
                return original_request
            
            logger.warning(f"测试计划文件中未找到 original_request 字段")
            return None
            
        except Exception as e:
            logger.error(f"加载测试计划文件失败 {plan_file_path}: {e}")
            return None
    
    def _extract_test_plan(self, content: str) -> Dict[str, Any]:
        """提取测试计划信息"""
        plan_info = {
            "summary": "",
            "platform": "",
            "total_planned_steps": 0,
            "steps": []
        }
        
        # 提取计划摘要
        summary_pattern = r'📝 计划描述: (.+)'
        summary_match = re.search(summary_pattern, content)
        if summary_match:
            plan_info["summary"] = summary_match.group(1).strip()
        
        # 提取平台信息
        platform_pattern = r'📱 目标平台: (\w+)'
        platform_match = re.search(platform_pattern, content)
        if platform_match:
            plan_info["platform"] = platform_match.group(1).strip()
        
        # 提取总步骤数
        steps_pattern = r'🔢 总步骤数: (\d+)'
        steps_match = re.search(steps_pattern, content)
        if steps_match:
            plan_info["total_planned_steps"] = int(steps_match.group(1))
        
        # 提取详细步骤
        step_pattern = r'步骤 (\d+): (.+?)\n.*?🔧 工具调用: (\w+)\n.*?📝 参数: (\{.*?\})\n.*?✅ 预期结果: (.+?)(?=\n\n|步骤|\n===|$)'
        steps = re.findall(step_pattern, content, re.DOTALL)
        
        for step_id, description, tool, params_str, expected in steps:
            try:
                params = json.loads(params_str)
            except:
                params = {"raw": params_str}
            
            plan_info["steps"].append({
                "step_id": int(step_id),
                "description": description.strip(),
                "tool": tool,
                "parameters": params,
                "expected_result": expected.strip()
            })
        
        return plan_info
    
    def _extract_execution_rounds(self, content: str) -> List[Dict[str, Any]]:
        """提取执行轮次信息"""
        rounds = []

        # 使用更精确的方法：先分割每一轮，再逐个解析
        round_sections = re.split(r'─── 第\d+轮执行 ───', content)[1:]  # 跳过第一个空部分
        round_numbers = re.findall(r'─── 第(\d+)轮执行 ───', content)

        for i, section in enumerate(round_sections):
            if i >= len(round_numbers):
                break

            round_num = round_numbers[i]
            try:
                # 检查是否是"直接回复用户"的轮次
                direct_reply_match = re.search(r'✅ 模型决定: 直接回复用户', section)
                if direct_reply_match:
                    # 这是一个直接回复轮次，不是工具调用
                    rounds.append({
                        "round": int(round_num),
                        "tool": "direct_reply",
                        "parameters": {"type": "direct_reply"},
                        "duration": 0.0,
                        "result": {"status": "success", "type": "direct_reply", "message": "模型直接回复用户"},
                        "summary": "模型直接回复用户",
                        "status": "success",
                        "success": True
                    })
                    continue

                # 提取工具名称
                tool_match = re.search(r'🔧 执行工具: (\w+)', section)
                tool = tool_match.group(1) if tool_match else "unknown"

                # 提取执行耗时
                duration_match = re.search(r'⏱️  执行耗时: ([\d.]+)秒', section)
                duration = float(duration_match.group(1)) if duration_match else 0.0

                # 提取结果摘要
                summary_match = re.search(r'💬 结果摘要: ([^\n]+)', section)
                summary = summary_match.group(1).strip() if summary_match else "N/A"

                # 提取工具参数 - 使用更健壮的方法
                params_match = re.search(r'📝 工具参数: ({.*?})\s*(?=\n|\s*⏱️)', section, re.DOTALL)
                if params_match:
                    params_str = params_match.group(1).strip()
                    params = self._safe_json_parse(params_str, f"第{round_num}轮参数")
                else:
                    params = {"error": "未找到参数"}

                # 提取执行结果 - 使用更复杂的匹配策略
                result_match = re.search(r'📊 执行结果: ({.*?})\s*(?=\n.*?💬|$)', section, re.DOTALL)
                if result_match:
                    result_str = result_match.group(1).strip()
                    result = self._safe_json_parse(result_str, f"第{round_num}轮结果")
                else:
                    result = {"error": "未找到结果"}

                # 确定状态
                status = "unknown"
                success = False
                if isinstance(result, dict):
                    status = result.get("status", "unknown")
                    success = status in ["success", "completed"]

                rounds.append({
                    "round": int(round_num),
                    "tool": tool,
                    "parameters": params,
                    "duration": duration,
                    "result": result,
                    "summary": summary,
                    "status": status,
                    "success": success
                })

            except Exception as e:
                logger.warning(f"解析第{round_num}轮执行失败: {e}")
                # 保存基本信息
                rounds.append({
                    "round": int(round_num),
                    "tool": "unknown",
                    "parameters": {"error": "解析失败"},
                    "duration": 0.0,
                    "result": {"error": "解析失败"},
                    "summary": "解析失败",
                    "status": "parse_error",
                    "success": False
                })

        return rounds
    
    def _safe_json_parse(self, json_str: str, context: str) -> Dict[str, Any]:
        """安全的JSON解析，处理常见的格式问题"""
        try:
            # 清理字符串
            cleaned = json_str.strip()
            
            # 尝试直接解析
            return json.loads(cleaned)
            
        except json.JSONDecodeError as e:
            try:
                # 尝试修复单引号问题
                fixed = cleaned.replace("'", '"')
                return json.loads(fixed)
                
            except json.JSONDecodeError:
                try:
                    # 尝试处理Python字典格式
                    import ast
                    return ast.literal_eval(cleaned)
                    
                except (ValueError, SyntaxError):
                    # 如果所有解析都失败，返回原始字符串
                    logger.warning(f"{context} JSON解析失败: {str(e)[:100]}...")
                    return {"raw": cleaned, "parse_error": str(e)}
    
    def _extract_completion_info(self, content: str) -> Dict[str, Any]:
        """提取任务完成信息"""
        completion_info = {}
        
        # 查找任务完成部分
        completion_pattern = r'任务完成.*?\n============================================================\n(.*?)============================================================'
        match = re.search(completion_pattern, content, re.DOTALL)
        
        if match:
            completion_content = match.group(1)
            
            # 提取各种信息
            patterns = {
                "total_rounds": r'📊 总轮数: (.+)',
                "total_duration": r'⏱️  总耗时: (.+)',
                "execution_status": r'✅ 执行状态: (.+)',
                "end_time": r'🕐 结束时间: (.+)'
            }
            
            for key, pattern in patterns.items():
                match = re.search(pattern, completion_content)
                if match:
                    completion_info[key] = match.group(1).strip()
        
        return completion_info
    
    def _ensure_judge_report_directory(self) -> str:
        """确保judge_report目录存在，返回目录路径"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        judge_report_dir = os.path.join(current_dir, "log", "judge_report")
        
        # 创建目录（如果不存在）
        os.makedirs(judge_report_dir, exist_ok=True)
        return judge_report_dir

    def _save_evaluation_to_json(self, log_data: Dict[str, Any], analysis_result: Dict[str, str],
                                judge_report_dir: str, round_name: str) -> str:
        """保存评价结果为JSON格式文件"""
        try:
            # 使用简单的文件名，只需要对应测试执行ID
            json_filename = f"{round_name}_evaluation.json"
            json_filepath = os.path.join(judge_report_dir, json_filename)

            # 构建JSON数据
            json_data = {
                "metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "round_name": round_name,
                    "round_number": log_data.get("round_number"),
                    "analysis_model": Path(self.model_path).name,
                    "output_format": "structured_json" if analysis_result.get('has_structured_output') else "text_format"
                },
                "test_info": {
                    "original_instruction": log_data.get('original_instruction', '未找到原始指令'),
                    "test_plan": log_data.get("test_plan", {}),
                    "execution_summary": {
                        "total_rounds": len(log_data.get("execution_rounds", [])),
                        "successful_rounds": len([r for r in log_data.get("execution_rounds", []) if r.get('success', False)]),
                        "total_duration": log_data.get("completion_info", {}).get('total_duration', '未知'),
                        "final_status": log_data.get("completion_info", {}).get('execution_status', '未知')
                    }
                },
                "raw_analysis": {
                    "thinking_process": analysis_result.get('thinking', ''),
                    "raw_output": analysis_result.get('analysis', ''),
                    "has_structured_output": analysis_result.get('has_structured_output', False)
                }
            }

            # 如果有结构化输出，添加解析后的JSON数据
            if analysis_result.get('has_structured_output') and analysis_result.get('parsed_json'):
                json_data["structured_evaluation"] = analysis_result.get('parsed_json')
            else:
                # 如果没有结构化输出，尝试从文本中提取关键信息
                json_data["text_evaluation"] = {
                    "content": analysis_result.get('analysis', ''),
                    "note": "此评价为文本格式，未能解析为结构化JSON"
                }

            # 添加执行详情（仅失败的轮次）
            failed_rounds = [r for r in log_data.get("execution_rounds", []) if not r.get("success")]
            if failed_rounds:
                json_data["execution_failures"] = []
                for round_data in failed_rounds:
                    round_num = log_data.get("execution_rounds", []).index(round_data) + 1
                    failure_info = {
                        "round_number": round_num,
                        "tool": round_data.get('tool', 'unknown'),
                        "summary": round_data.get('summary', 'N/A'),
                        "error_message": round_data.get("result", {}).get('message', ''),
                        "duration": round_data.get('duration', 0.0)
                    }
                    json_data["execution_failures"].append(failure_info)

            # 保存到文件
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            logger.info(f"📁 JSON评价报告已保存到: {json_filepath}")
            return json_filepath

        except Exception as e:
            logger.error(f"保存JSON评价报告失败: {e}")
            # 返回一个默认文件名，即使保存失败
            return os.path.join(judge_report_dir, f"{round_name}_evaluation.json")

    def _extract_round_name_from_path(self, round_folder_path: str) -> str:
        """从轮次文件夹路径提取轮次名称"""
        folder_name = Path(round_folder_path).name
        return folder_name
    
    def judge_test_execution(self, round_folder_path: str) -> str:
        """评价测试执行
        
        Args:
            round_folder_path: 轮次文件夹路径
        
        Returns:
            评价报告内容
        """
        try:
            folder_path = Path(round_folder_path)
            if not folder_path.exists():
                return f"❌ 文件夹不存在: {round_folder_path}"
            
            log_file = folder_path / "task_structured.log"
            if not log_file.exists():
                return f"❌ 日志文件不存在: {log_file}"
            
            logger.info(f"🔍 开始分析测试轮次: {folder_path.name}")
            
            # 解析日志
            log_data = self.parse_test_log(str(log_file))
            if "error" in log_data:
                return f"❌ 日志解析失败: {log_data['error']}"
            
            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(log_data)
            
            # 调用LLM进行分析
            system_prompt = self._get_system_prompt()
            analysis_result = self._call_llm(analysis_prompt, system_prompt)

            # 保存JSON格式的评价结果
            judge_report_dir = self._ensure_judge_report_directory()
            round_name = self._extract_round_name_from_path(round_folder_path)

            # 保存JSON格式文件
            json_report_file = self._save_evaluation_to_json(log_data, analysis_result, judge_report_dir, round_name)

            # 格式化最终报告（用于返回和显示）
            final_report = self._format_final_report(log_data, analysis_result)

            logger.info(f"✅ 分析完成: {folder_path.name}")
            logger.info(f"📄 报告已保存到: {json_report_file}")
            return final_report
            
        except Exception as e:
            logger.error(f"评价过程异常: {e}")
            return f"❌ 评价过程异常: {str(e)}"
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        try:
            system_prompt_template = self.prompt_templates.get("system_prompt", {})

            # 构建基本信息
            parts = []
            parts.append(system_prompt_template.get("role_description", ""))
            parts.append(system_prompt_template.get("core_task", ""))

            # 添加分析维度说明
            analysis_dimensions = system_prompt_template.get("analysis_dimensions", {})
            if analysis_dimensions:
                parts.append(analysis_dimensions.get("header", ""))
                for dimension in analysis_dimensions.get("dimensions", []):
                    parts.append(dimension)
                parts.append("")

            # 添加评价标准
            evaluation_criteria = system_prompt_template.get("evaluation_criteria", {})
            if evaluation_criteria:
                parts.append(evaluation_criteria.get("header", ""))
                for criterion in evaluation_criteria.get("criteria", []):
                    parts.append(criterion)
                parts.append("")

            # 构建输出格式
            output_format = system_prompt_template.get("output_format", {})
            if output_format:
                parts.append(output_format.get("header", ""))
                parts.append(output_format.get("description", ""))

                # 添加JSON模板
                json_template = output_format.get("json_template", {})
                if json_template:
                    json_str = json.dumps(json_template, ensure_ascii=False, indent=2)
                    parts.append(json_str)
                    parts.append("")

            # 添加重要说明
            important_notes = system_prompt_template.get("important_notes", {})
            if important_notes:
                parts.append(important_notes.get("header", ""))
                for note in important_notes.get("notes", []):
                    parts.append(note)
                parts.append("")

            # 添加最终指令
            parts.append(system_prompt_template.get("final_instruction", ""))

            return "\n\n".join(filter(None, parts))

        except Exception as e:
            logger.error(f"构建系统提示词失败: {e}")
            # 返回备用的简化版本
            return """你是一个专业的自动化测试评价专家。请分析测试执行情况并给出JSON格式的评价报告。

请按照JSON格式输出，包含evaluation_summary、dimension_scores、detailed_analysis和comprehensive_evaluation四个主要部分。"""

    def _build_analysis_prompt(self, log_data: Dict[str, Any]) -> str:
        """构建分析提示词"""
        try:
            template = self.prompt_templates.get("analysis_prompt_template", {})
            
            execution_rounds = log_data.get("execution_rounds", [])
            completion_info = log_data.get("completion_info", {})
            test_plan = log_data.get("test_plan", {})
            plan_steps = test_plan.get('steps', [])
            
            # 构建详细步骤信息
            detailed_steps = ""
            if plan_steps:
                for i, step in enumerate(plan_steps, 1):
                    detailed_steps += f"""
步骤{i}: {step.get('description', 'N/A')}
  工具: {step.get('tool', 'unknown')}
  参数: {json.dumps(step.get('parameters', {}), ensure_ascii=False)}
  预期: {step.get('expected_result', 'N/A')}"""
            else:
                detailed_steps = "\n（从日志中未解析出详细步骤信息）"
            
            # 构建执行详情
            execution_details = ""
            for i, round_data in enumerate(execution_rounds, 1):
                status = "✅" if round_data.get("success") else "❌"
                execution_details += f"""
第{i}轮 {status} {round_data.get('tool', 'unknown')}
  参数: {json.dumps(round_data.get('parameters', {}), ensure_ascii=False)}
  结果: {round_data.get('summary', 'N/A')}"""
                
                # 显示错误信息（如果有）
                result = round_data.get('result', {})
                if isinstance(result, dict) and 'message' in result and not round_data.get("success"):
                    execution_details += f"""
  错误: {result['message']}"""
            
            # 使用模板构建prompt
            prompt_parts = []
            prompt_parts.append(template.get("header", "请基于以下三部分原始信息进行专业的测试质量分析："))
            
            # 添加各个部分
            sections = template.get("sections", [])
            for section in sections:
                title = section.get("title", "")
                if "原始测试指令" in title:
                    prompt_parts.append(title)
                    prompt_parts.append(log_data.get('original_instruction', '未找到原始指令'))
                elif "结构化测试计划" in title:
                    prompt_parts.append(title)
                    fields = section.get("fields", [])
                    for field in fields:
                        field_formatted = field.format(
                            plan_summary=test_plan.get('summary', '无'),
                            target_platform=test_plan.get('platform', '未知'),
                            total_planned_steps=test_plan.get('total_planned_steps', 0)
                        )
                        prompt_parts.append(field_formatted)
                    
                    detailed_steps_section = section.get("detailed_steps", {})
                    if detailed_steps_section:
                        prompt_parts.append(detailed_steps_section.get("title", ""))
                        prompt_parts.append(detailed_steps)
                        
                elif "实际执行日志" in title:
                    prompt_parts.append(title)
                    execution_stats = section.get("execution_stats", "").format(
                        execution_count=len(execution_rounds),
                        success_count=len([r for r in execution_rounds if r.get('success', False)]),
                        total_duration=completion_info.get('total_duration', '未知')
                    )
                    prompt_parts.append(execution_stats)
                    
                    execution_details_section = section.get("execution_details", {})
                    if execution_details_section:
                        prompt_parts.append(execution_details_section.get("title", ""))
                        prompt_parts.append(execution_details)
            
            # 添加分析要求
            analysis_requirements = template.get("analysis_requirements", {})
            if analysis_requirements:
                prompt_parts.append(analysis_requirements.get("title", ""))
                prompt_parts.append(analysis_requirements.get("description", ""))
                dimensions = analysis_requirements.get("dimensions", [])
                for dimension in dimensions:
                    prompt_parts.append(dimension)
                prompt_parts.append(analysis_requirements.get("focus", ""))
            
            return "\n\n".join(filter(None, prompt_parts))
            
        except Exception as e:
            logger.error(f"构建分析提示词失败: {e}")
            # 返回备用的简化版本
            execution_rounds = log_data.get("execution_rounds", [])
            completion_info = log_data.get("completion_info", {})
            test_plan = log_data.get("test_plan", {})
            
            return f"""请分析以下测试执行情况：

原始指令: {log_data.get('original_instruction', '未找到原始指令')}

测试计划: {test_plan.get('summary', '无')}
平台: {test_plan.get('platform', '未知')}
计划步骤数: {test_plan.get('total_planned_steps', 0)}

执行统计: 共{len(execution_rounds)}轮，成功{len([r for r in execution_rounds if r.get('success', False)])}轮

请给出专业的测试质量评价。"""

    def _format_final_report(self, log_data: Dict[str, Any], analysis_result: Dict[str, str]) -> str:
        """格式化最终报告"""

        folder_name = Path(log_data.get("log_path", "")).parent.name
        round_number = log_data.get("round_number")
        test_plan = log_data.get("test_plan", {})
        execution_rounds = log_data.get("execution_rounds", [])
        completion_info = log_data.get("completion_info", {})

        # 获取原始指令
        original_instruction = log_data.get('original_instruction', '未找到原始指令')

        # 分离思考过程和最终分析
        thinking_content = analysis_result.get('thinking', '')
        final_analysis = analysis_result.get('analysis', '')
        parsed_json = analysis_result.get('parsed_json')
        has_structured_output = analysis_result.get('has_structured_output', False)

        report = f"""
{'='*80}
自动化测试质量评价报告
{'='*80}

📁 测试轮次: {folder_name}
🔢 轮次编号: {round_number if round_number else '未知'}
⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 分析模型: {Path(self.model_path).name}
📊 输出格式: {'结构化JSON' if has_structured_output else '文本格式'}

{'='*80}
测试信息概览
{'='*80}

📋 原始指令:
{original_instruction}

📊 执行统计:
• 计划步骤: {test_plan.get('total_planned_steps', 0)} 步
• 实际执行: {len(execution_rounds)} 轮 (成功 {len([r for r in execution_rounds if r.get('success', False)])} 轮)
• 总耗时: {completion_info.get('total_duration', '未知')}
• 最终状态: {completion_info.get('execution_status', '未知')}"""

        # 如果有结构化JSON输出，优先显示结构化内容
        if has_structured_output and parsed_json:
            report += self._format_structured_evaluation(parsed_json)
        else:
            # 如果有思考过程，先显示思考过程
            if thinking_content:
                report += f"""

{'='*80}
分析思考过程
{'='*80}

{thinking_content}"""

            # 然后显示专业分析评价
            report += f"""

{'='*80}
专业分析评价
{'='*80}

{final_analysis}"""
        
        # 只显示失败的执行步骤
        failed_rounds = [r for r in execution_rounds if not r.get("success")]
        if failed_rounds:
            report += f"""

{'='*80}
执行失败详情
{'='*80}"""
            
            for round_data in failed_rounds:
                round_num = execution_rounds.index(round_data) + 1
                report += f"""
第{round_num}轮执行失败:
  工具: {round_data.get('tool', 'unknown')}
  摘要: {round_data.get('summary', 'N/A')}"""
                
                result = round_data.get("result", {})
                if "message" in result:
                    report += f"""
  错误: {result['message']}"""

        report += f"""

{'='*80}
报告生成完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*80}
"""
        
        return report


def main():
    """主函数 - 用于测试"""
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python agent_process_judge_without_rag.py <round_folder_path>")
        print("示例: python agent_process_judge_without_rag.py simplify_agent/log/agent_execute_log/round_000005_20250903_181343")
        sys.exit(1)
    
    round_folder = sys.argv[1]
    
    # 创建评价器（使用MLX）
    judge = TestProcessJudge(
        model_path=DEFAULT_MODEL_PATH,
        max_tokens=DEFAULT_MAX_TOKENS
    )
    
    # 执行评价（保存到统一的judge_report目录）
    result = judge.judge_test_execution(round_folder)
    
    # 打印结果
    print(result)
    
    # 显示保存位置
    round_name = Path(round_folder).name
    judge_report_dir = judge._ensure_judge_report_directory()
    judge_report_path = os.path.join(judge_report_dir, f"{round_name}_report.log")
    print(f"\n📄 报告已保存到: {judge_report_path}")


if __name__ == "__main__":
    main()